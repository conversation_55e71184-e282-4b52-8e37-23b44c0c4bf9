variable "availability_zone" {
  default = "ap-hongkong-2"
}

data "tencentcloud_images" "images" {
  image_type       = ["PUBLIC_IMAGE"]
  image_name_regex = "TencentOS Server"
}

resource "tencentcloud_vpc" "vpc" {
  name       = "vpc"
  cidr_block = "10.0.0.0/16"
}

resource "tencentcloud_subnet" "subnet" {
  name              = "subnet"
  vpc_id            = tencentcloud_vpc.vpc.id
  availability_zone = var.availability_zone
  cidr_block        = "*********/28"
  is_multicast      = false
}

resource "tencentcloud_instance" "spot_example" {
  instance_name     = "tf_spot_example"
  availability_zone = var.availability_zone
  image_id          = data.tencentcloud_images.images.images.0.image_id
  instance_type     = "SA3.MEDIUM4"

  # 系统盘用官方示例的方式，不用 block
  system_disk_type  = "CLOUD_HSSD"
  system_disk_size  = 100

  hostname          = "spot-example"
  project_id        = 0
  vpc_id            = tencentcloud_vpc.vpc.id
  subnet_id         = tencentcloud_subnet.subnet.id

  # 如果需要数据盘
  data_disks {
    data_disk_type = "CLOUD_HSSD"
    data_disk_size = 50
    encrypt        = false
  }

  # 添加竞价实例相关字段（如果当前版本支持的话）
  instance_charge_type = "SPOTPAID"
  spot_instance_type   = "ONE_TIME"   # 或 "SPOTPAID" 长期竞价类型，依据支持情况

  tags = {
    createBy = "terraform"
  }
}